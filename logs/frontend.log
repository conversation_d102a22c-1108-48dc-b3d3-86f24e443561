当前工作目录: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend
启动语义化URL服务器在端口 8083...
服务器地址: http://localhost:8083

支持的语义化URL模式:
- /authors/ -> authors.html
- /authors/{slug}/ -> author.html
- /authors/{slug}/quotes/ -> author.html
- /categories/ -> categories.html
- /categories/{slug}/ -> category.html
- /categories/{slug}/quotes/ -> category.html
- /sources/ -> sources.html
- /sources/{slug}/ -> source.html
- /quotes/ -> quotes.html
- /quotes/{id}/ -> quote.html
- /search/ -> search.html

按 Ctrl+C 停止服务器
🔍 收到GET请求: /
🔍 解析路径: /
❌ 未匹配到任何语义化URL模式: /
[127.0.0.1] "GET / HTTP/1.1" 200 -
🔍 收到GET请求: /
🔍 解析路径: /
❌ 未匹配到任何语义化URL模式: /
[127.0.0.1] "GET / HTTP/1.1" 200 -
🔍 收到GET请求: /
🔍 解析路径: /
❌ 未匹配到任何语义化URL模式: /
[127.0.0.1] "GET / HTTP/1.1" 304 -
🔍 收到GET请求: /js/config.js?v=20250623
🔍 解析路径: /js/config.js
❌ 未匹配到任何语义化URL模式: /js/config.js
✅ 静态文件请求: /js/config.js
[127.0.0.1] "GET /js/config.js?v=20250623 HTTP/1.1" 304 -
🔍 收到GET请求: /js/mock-data.js?v=20250623
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250623 HTTP/1.1" 304 -
🔍 收到GET请求: /js/api-client.js?v=20250623
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250623 HTTP/1.1" 304 -
🔍 收到GET请求: /js/entity-id-mapper.js?v=20250623
🔍 解析路径: /js/entity-id-mapper.js
❌ 未匹配到任何语义化URL模式: /js/entity-id-mapper.js
✅ 静态文件请求: /js/entity-id-mapper.js
[127.0.0.1] "GET /js/entity-id-mapper.js?v=20250623 HTTP/1.1" 304 -
🔍 收到GET请求: /js/url-handler.js?v=20250623
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250623 HTTP/1.1" 304 -
🔍 收到GET请求: /js/components/quote-card.js?v=20250623
🔍 解析路径: /js/components/quote-card.js
❌ 未匹配到任何语义化URL模式: /js/components/quote-card.js
✅ 静态文件请求: /js/components/quote-card.js
[127.0.0.1] "GET /js/components/quote-card.js?v=20250623 HTTP/1.1" 304 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250623
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250623 HTTP/1.1" 304 -
🔍 收到GET请求: /js/navigation-state.js?v=20250627
🔍 解析路径: /js/navigation-state.js
❌ 未匹配到任何语义化URL模式: /js/navigation-state.js
✅ 静态文件请求: /js/navigation-state.js
[127.0.0.1] "GET /js/navigation-state.js?v=20250627 HTTP/1.1" 304 -
🔍 收到GET请求: /js/quote-card-click-fix.js
🔍 解析路径: /js/quote-card-click-fix.js
❌ 未匹配到任何语义化URL模式: /js/quote-card-click-fix.js
✅ 静态文件请求: /js/quote-card-click-fix.js
[127.0.0.1] code 404, message File not found
[127.0.0.1] "GET /js/quote-card-click-fix.js HTTP/1.1" 404 -
🔍 收到GET请求: /js/quote-card-click-fix.js
🔍 解析路径: /js/quote-card-click-fix.js
❌ 未匹配到任何语义化URL模式: /js/quote-card-click-fix.js
✅ 静态文件请求: /js/quote-card-click-fix.js
[127.0.0.1] code 404, message File not found
[127.0.0.1] "GET /js/quote-card-click-fix.js HTTP/1.1" 404 -
🔍 收到GET请求: /categories/
🔍 解析路径: /categories/
✅ 匹配到语义化URL模式: ^/categories/$ -> categories.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/categories.html
   ✅ 重写路径为: /categories.html
未匹配的路径，可能需要添加新的URL模式: /categories/
重定向到 categories.html
[127.0.0.1] "GET /categories/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/categories.js?v=20250626
🔍 解析路径: /js/pages/categories.js
❌ 未匹配到任何语义化URL模式: /js/pages/categories.js
✅ 静态文件请求: /js/pages/categories.js
[127.0.0.1] "GET /js/pages/categories.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/test-breadcrumb.js?v=20250627
🔍 解析路径: /js/test-breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/test-breadcrumb.js
✅ 静态文件请求: /js/test-breadcrumb.js
[127.0.0.1] "GET /js/test-breadcrumb.js?v=20250627 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /css/performance-optimizations.css
🔍 解析路径: /css/performance-optimizations.css
❌ 未匹配到任何语义化URL模式: /css/performance-optimizations.css
✅ 静态文件请求: /css/performance-optimizations.css
[127.0.0.1] "GET /css/performance-optimizations.css HTTP/1.1" 304 -
🔍 收到GET请求: /js/performance-monitor.js?v=20250627
🔍 解析路径: /js/performance-monitor.js
❌ 未匹配到任何语义化URL模式: /js/performance-monitor.js
✅ 静态文件请求: /js/performance-monitor.js
[127.0.0.1] "GET /js/performance-monitor.js?v=20250627 HTTP/1.1" 304 -
🔍 收到GET请求: /js/config.js?v=20250626
🔍 解析路径: /js/config.js
❌ 未匹配到任何语义化URL模式: /js/config.js
✅ 静态文件请求: /js/config.js
[127.0.0.1] "GET /js/config.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /authors/
🔍 解析路径: /authors/
✅ 匹配到语义化URL模式: ^/authors/$ -> authors.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/authors.html
   ✅ 重写路径为: /authors.html
未匹配的路径，可能需要添加新的URL模式: /authors/
重定向到 authors.html
[127.0.0.1] "GET /authors/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/authors.js?v=20250626
🔍 解析路径: /js/pages/authors.js
❌ 未匹配到任何语义化URL模式: /js/pages/authors.js
✅ 静态文件请求: /js/pages/authors.js
[127.0.0.1] "GET /js/pages/authors.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /sources/
🔍 解析路径: /sources/
✅ 匹配到语义化URL模式: ^/sources/$ -> sources.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/sources.html
   ✅ 重写路径为: /sources.html
未匹配的路径，可能需要添加新的URL模式: /sources/
重定向到 sources.html
[127.0.0.1] "GET /sources/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/sources.js?v=20250626
🔍 解析路径: /js/pages/sources.js
❌ 未匹配到任何语义化URL模式: /js/pages/sources.js
✅ 静态文件请求: /js/pages/sources.js
[127.0.0.1] "GET /js/pages/sources.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/quote-card-click-fix.js
🔍 解析路径: /js/quote-card-click-fix.js
❌ 未匹配到任何语义化URL模式: /js/quote-card-click-fix.js
✅ 静态文件请求: /js/quote-card-click-fix.js
[127.0.0.1] code 404, message File not found
[127.0.0.1] "GET /js/quote-card-click-fix.js HTTP/1.1" 404 -
🔍 收到GET请求: /categories/self/
🔍 解析路径: /categories/self/
✅ 匹配到语义化URL模式: ^/categories/([a-zA-Z0-9\-_]+)/$ -> category.html
   匹配组: ('self',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/category.html
   ✅ 重写路径为: /category.html
未匹配的路径，可能需要添加新的URL模式: /categories/self/
重定向到 category.html
[127.0.0.1] "GET /categories/self/ HTTP/1.1" 304 -
🔍 收到GET请求: /js/performance-test.js?v=20250626
🔍 解析路径: /js/performance-test.js
❌ 未匹配到任何语义化URL模式: /js/performance-test.js
✅ 静态文件请求: /js/performance-test.js
[127.0.0.1] "GET /js/performance-test.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/entity-id-mapper-production.js?v=20250627
🔍 解析路径: /js/entity-id-mapper-production.js
❌ 未匹配到任何语义化URL模式: /js/entity-id-mapper-production.js
✅ 静态文件请求: /js/entity-id-mapper-production.js
[127.0.0.1] "GET /js/entity-id-mapper-production.js?v=20250627 HTTP/1.1" 304 -
🔍 收到GET请求: /js/entity-id-mapper.js?v=20250627
🔍 解析路径: /js/entity-id-mapper.js
❌ 未匹配到任何语义化URL模式: /js/entity-id-mapper.js
✅ 静态文件请求: /js/entity-id-mapper.js
[127.0.0.1] "GET /js/entity-id-mapper.js?v=20250627 HTTP/1.1" 304 -
🔍 收到GET请求: /js/mobile-performance-optimizer.js?v=20250626
🔍 解析路径: /js/mobile-performance-optimizer.js
❌ 未匹配到任何语义化URL模式: /js/mobile-performance-optimizer.js
✅ 静态文件请求: /js/mobile-performance-optimizer.js
[127.0.0.1] "GET /js/mobile-performance-optimizer.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/components/quote-card.js?v=20250626
🔍 解析路径: /js/components/quote-card.js
❌ 未匹配到任何语义化URL模式: /js/components/quote-card.js
✅ 静态文件请求: /js/components/quote-card.js
[127.0.0.1] "GET /js/components/quote-card.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/pages/category.js?v=20250626
🔍 解析路径: /js/pages/category.js
❌ 未匹配到任何语义化URL模式: /js/pages/category.js
✅ 静态文件请求: /js/pages/category.js
[127.0.0.1] "GET /js/pages/category.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /authors/nikki-rowe/
🔍 解析路径: /authors/nikki-rowe/
✅ 匹配到语义化URL模式: ^/authors/([a-zA-Z0-9\-_]+)/$ -> author.html
   匹配组: ('nikki-rowe',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/author.html
   ✅ 重写路径为: /author.html
未匹配的路径，可能需要添加新的URL模式: /authors/nikki-rowe/
重定向到 author.html
[127.0.0.1] "GET /authors/nikki-rowe/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/author.js?v=20250626
🔍 解析路径: /js/pages/author.js
❌ 未匹配到任何语义化URL模式: /js/pages/author.js
✅ 静态文件请求: /js/pages/author.js
[127.0.0.1] "GET /js/pages/author.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /authors/toni-sorenson/
🔍 解析路径: /authors/toni-sorenson/
✅ 匹配到语义化URL模式: ^/authors/([a-zA-Z0-9\-_]+)/$ -> author.html
   匹配组: ('toni-sorenson',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/author.html
   ✅ 重写路径为: /author.html
未匹配的路径，可能需要添加新的URL模式: /authors/toni-sorenson/
重定向到 author.html
[127.0.0.1] "GET /authors/toni-sorenson/ HTTP/1.1" 200 -
🔍 收到GET请求: /authors/mensah-oteh/
🔍 解析路径: /authors/mensah-oteh/
✅ 匹配到语义化URL模式: ^/authors/([a-zA-Z0-9\-_]+)/$ -> author.html
   匹配组: ('mensah-oteh',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/author.html
   ✅ 重写路径为: /author.html
未匹配的路径，可能需要添加新的URL模式: /authors/mensah-oteh/
重定向到 author.html
[127.0.0.1] "GET /authors/mensah-oteh/ HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 304 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 304 -
🔍 收到GET请求: /quotes/js/pages/quotes.js
🔍 解析路径: /quotes/js/pages/quotes.js
❌ 未匹配到任何语义化URL模式: /quotes/js/pages/quotes.js
🔧 修复静态文件路径: /quotes/js/pages/quotes.js → /js/pages/quotes.js
[127.0.0.1] code 404, message File not found
[127.0.0.1] "GET /quotes/js/pages/quotes.js HTTP/1.1" 404 -
🔍 收到GET请求: /categories/work/
🔍 解析路径: /categories/work/
✅ 匹配到语义化URL模式: ^/categories/([a-zA-Z0-9\-_]+)/$ -> category.html
   匹配组: ('work',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/category.html
   ✅ 重写路径为: /category.html
未匹配的路径，可能需要添加新的URL模式: /categories/work/
重定向到 category.html
[127.0.0.1] "GET /categories/work/ HTTP/1.1" 200 -
🔍 收到GET请求: /sources/write-like-no-one-is-reading/
🔍 解析路径: /sources/write-like-no-one-is-reading/
✅ 匹配到语义化URL模式: ^/sources/([a-zA-Z0-9\-_]+)/$ -> source.html
   匹配组: ('write-like-no-one-is-reading',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/source.html
   ✅ 重写路径为: /source.html
未匹配的路径，可能需要添加新的URL模式: /sources/write-like-no-one-is-reading/
重定向到 source.html
[127.0.0.1] "GET /sources/write-like-no-one-is-reading/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/source.js?v=20250626
🔍 解析路径: /js/pages/source.js
❌ 未匹配到任何语义化URL模式: /js/pages/source.js
✅ 静态文件请求: /js/pages/source.js
[127.0.0.1] "GET /js/pages/source.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/js/pages/quotes.js
🔍 解析路径: /quotes/js/pages/quotes.js
❌ 未匹配到任何语义化URL模式: /quotes/js/pages/quotes.js
🔧 修复静态文件路径: /quotes/js/pages/quotes.js → /js/pages/quotes.js
[127.0.0.1] "GET /quotes/js/pages/quotes.js HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/js/pages/quotes.js
🔍 解析路径: /quotes/js/pages/quotes.js
❌ 未匹配到任何语义化URL模式: /quotes/js/pages/quotes.js
🔧 修复静态文件路径: /quotes/js/pages/quotes.js → /js/pages/quotes.js
[127.0.0.1] "GET /quotes/js/pages/quotes.js HTTP/1.1" 304 -
🔍 收到GET请求: /test-quotes-page.html
🔍 解析路径: /test-quotes-page.html
❌ 未匹配到任何语义化URL模式: /test-quotes-page.html
[127.0.0.1] "GET /test-quotes-page.html HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quotes.js?v=20250626
🔍 解析路径: /js/pages/quotes.js
❌ 未匹配到任何语义化URL模式: /js/pages/quotes.js
✅ 静态文件请求: /js/pages/quotes.js
[127.0.0.1] "GET /js/pages/quotes.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/js/pages/quotes.js
🔍 解析路径: /quotes/js/pages/quotes.js
❌ 未匹配到任何语义化URL模式: /quotes/js/pages/quotes.js
🔧 修复静态文件路径: /quotes/js/pages/quotes.js → /js/pages/quotes.js
[127.0.0.1] "GET /quotes/js/pages/quotes.js HTTP/1.1" 304 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 200 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 200 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/499276/
🔍 解析路径: /quotes/499276/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('499276',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quote.html
   ✅ 重写路径为: /quote.html
未匹配的路径，可能需要添加新的URL模式: /quotes/499276/
重定向到 quote.html
[127.0.0.1] "GET /quotes/499276/ HTTP/1.1" 304 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 304 -
🔍 收到GET请求: /js/components/navigation.js
🔍 解析路径: /js/components/navigation.js
❌ 未匹配到任何语义化URL模式: /js/components/navigation.js
✅ 静态文件请求: /js/components/navigation.js
[127.0.0.1] "GET /js/components/navigation.js HTTP/1.1" 304 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/pages/quote.js?v=20250628
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250628 HTTP/1.1" 304 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 304 -
🔍 收到GET请求: /components/navigation.html
🔍 解析路径: /components/navigation.html
❌ 未匹配到任何语义化URL模式: /components/navigation.html
✅ 静态文件请求: /components/navigation.html
[127.0.0.1] "GET /components/navigation.html HTTP/1.1" 304 -
🔍 收到GET请求: /js/components/navigation.js
🔍 解析路径: /js/components/navigation.js
❌ 未匹配到任何语义化URL模式: /js/components/navigation.js
✅ 静态文件请求: /js/components/navigation.js
[127.0.0.1] "GET /js/components/navigation.js HTTP/1.1" 304 -
🔍 收到GET请求: /quotes/js/pages/quotes.js
🔍 解析路径: /quotes/js/pages/quotes.js
❌ 未匹配到任何语义化URL模式: /quotes/js/pages/quotes.js
🔧 修复静态文件路径: /quotes/js/pages/quotes.js → /js/pages/quotes.js
[127.0.0.1] "GET /quotes/js/pages/quotes.js HTTP/1.1" 200 -
🔍 收到GET请求: /components/navigation.html
🔍 解析路径: /components/navigation.html
❌ 未匹配到任何语义化URL模式: /components/navigation.html
✅ 静态文件请求: /components/navigation.html
[127.0.0.1] "GET /components/navigation.html HTTP/1.1" 200 -
🔍 收到GET请求: /components/footer.html
🔍 解析路径: /components/footer.html
❌ 未匹配到任何语义化URL模式: /components/footer.html
✅ 静态文件请求: /components/footer.html
[127.0.0.1] "GET /components/footer.html HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/navigation.js
🔍 解析路径: /js/components/navigation.js
❌ 未匹配到任何语义化URL模式: /js/components/navigation.js
✅ 静态文件请求: /js/components/navigation.js
[127.0.0.1] "GET /js/components/navigation.js HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/footer.js
🔍 解析路径: /js/components/footer.js
❌ 未匹配到任何语义化URL模式: /js/components/footer.js
✅ 静态文件请求: /js/components/footer.js
[127.0.0.1] "GET /js/components/footer.js HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/js/pages/quotes.js
🔍 解析路径: /quotes/js/pages/quotes.js
❌ 未匹配到任何语义化URL模式: /quotes/js/pages/quotes.js
🔧 修复静态文件路径: /quotes/js/pages/quotes.js → /js/pages/quotes.js----------------------------------------
Exception occurred during processing of request from ('127.0.0.1', 61164)
Traceback (most recent call last):
  File "/Users/<USER>/Documents/quotese_0503_0629_V1/frontend/semantic_url_server.py", line 206, in do_GET
    super().do_GET()
    ~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 676, in do_GET
    f = self.send_head()
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 762, in send_head
    self.end_headers()
    ~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Documents/quotese_0503_0629_V1/frontend/semantic_url_server.py", line 216, in end_headers
    super().end_headers()
    ~~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 538, in end_headers
    self.flush_headers()
    ~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 542, in flush_headers
    self.wfile.write(b"".join(self._headers_buffer))
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 845, in write
    self._sock.sendall(b)
    ~~~~~~~~~~~~~~~~~~^^^
BrokenPipeError: [Errno 32] Broken pipe

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 318, in _handle_request_noblock
    self.process_request(request, client_address)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 349, in process_request
    self.finish_request(request, client_address)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/quotese_0503_0629_V1/frontend/semantic_url_server.py", line 22, in __init__
    super().__init__(*args, **kwargs)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 672, in __init__
    super().__init__(*args, **kwargs)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 766, in __init__
    self.handle()
    ~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 436, in handle
    self.handle_one_request()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 424, in handle_one_request
    method()
    ~~~~~~^^
  File "/Users/<USER>/Documents/quotese_0503_0629_V1/frontend/semantic_url_server.py", line 209, in do_GET
    self.send_error(500, f"Internal server error: {e}")
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 488, in send_error
    self.end_headers()
    ~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Documents/quotese_0503_0629_V1/frontend/semantic_url_server.py", line 216, in end_headers
    super().end_headers()
    ~~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 538, in end_headers
    self.flush_headers()
    ~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 542, in flush_headers
    self.wfile.write(b"".join(self._headers_buffer))
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 845, in write
    self._sock.sendall(b)
    ~~~~~~~~~~~~~~~~~~^^^
BrokenPipeError: [Errno 32] Broken pipe
----------------------------------------
----------------------------------------
Exception occurred during processing of request from ('127.0.0.1', 61165)
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 318, in _handle_request_noblock
    self.process_request(request, client_address)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 349, in process_request
    self.finish_request(request, client_address)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/quotese_0503_0629_V1/frontend/semantic_url_server.py", line 22, in __init__
    super().__init__(*args, **kwargs)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 672, in __init__
    super().__init__(*args, **kwargs)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 766, in __init__
    self.handle()
    ~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 436, in handle
    self.handle_one_request()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 404, in handle_one_request
    self.raw_requestline = self.rfile.readline(65537)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
ConnectionResetError: [Errno 54] Connection reset by peer
----------------------------------------

[127.0.0.1] "GET /quotes/js/pages/quotes.js HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /css/styles.css
🔍 解析路径: /css/styles.css
❌ 未匹配到任何语义化URL模式: /css/styles.css
✅ 静态文件请求: /css/styles.css
[127.0.0.1] "GET /css/styles.css HTTP/1.1" 304 -
🔍 收到GET请求: /js/pages/quote.js?v=20250626
🔍 解析路径: /js/pages/quote.js
❌ 未匹配到任何语义化URL模式: /js/pages/quote.js
✅ 静态文件请求: /js/pages/quote.js
[127.0.0.1] "GET /js/pages/quote.js?v=20250626 HTTP/1.1" 200 -
🔍 收到GET请求: /css/animations.css
🔍 解析路径: /css/animations.css
❌ 未匹配到任何语义化URL模式: /css/animations.css
✅ 静态文件请求: /css/animations.css
[127.0.0.1] "GET /css/animations.css HTTP/1.1" 304 -
🔍 收到GET请求: /js/analytics.js?v=20250626
🔍 解析路径: /js/analytics.js
❌ 未匹配到任何语义化URL模式: /js/analytics.js
✅ 静态文件请求: /js/analytics.js
[127.0.0.1] "GET /js/analytics.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/debug.js?v=20250626
🔍 解析路径: /js/debug.js
❌ 未匹配到任何语义化URL模式: /js/debug.js
✅ 静态文件请求: /js/debug.js
[127.0.0.1] "GET /js/debug.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/component-loader.js?v=20250626
🔍 解析路径: /js/component-loader.js
❌ 未匹配到任何语义化URL模式: /js/component-loader.js
✅ 静态文件请求: /js/component-loader.js
[127.0.0.1] "GET /js/component-loader.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/mock-data.js?v=20250626
🔍 解析路径: /js/mock-data.js
❌ 未匹配到任何语义化URL模式: /js/mock-data.js
✅ 静态文件请求: /js/mock-data.js
[127.0.0.1] "GET /js/mock-data.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/api-client.js?v=20250626
🔍 解析路径: /js/api-client.js
❌ 未匹配到任何语义化URL模式: /js/api-client.js
✅ 静态文件请求: /js/api-client.js
[127.0.0.1] "GET /js/api-client.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/theme.js?v=20250626
🔍 解析路径: /js/theme.js
❌ 未匹配到任何语义化URL模式: /js/theme.js
✅ 静态文件请求: /js/theme.js
[127.0.0.1] "GET /js/theme.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /css/variables.css
🔍 解析路径: /css/variables.css
❌ 未匹配到任何语义化URL模式: /css/variables.css
✅ 静态文件请求: /css/variables.css
[127.0.0.1] "GET /css/variables.css HTTP/1.1" 304 -
🔍 收到GET请求: /css/buttons.css
🔍 解析路径: /css/buttons.css
❌ 未匹配到任何语义化URL模式: /css/buttons.css
✅ 静态文件请求: /css/buttons.css
[127.0.0.1] "GET /css/buttons.css HTTP/1.1" 304 -
🔍 收到GET请求: /css/responsive.css
🔍 解析路径: /css/responsive.css
❌ 未匹配到任何语义化URL模式: /css/responsive.css
✅ 静态文件请求: /css/responsive.css
[127.0.0.1] "GET /css/responsive.css HTTP/1.1" 304 -
🔍 收到GET请求: /js/url-handler.js?v=20250626
🔍 解析路径: /js/url-handler.js
❌ 未匹配到任何语义化URL模式: /js/url-handler.js
✅ 静态文件请求: /js/url-handler.js
[127.0.0.1] "GET /js/url-handler.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/seo-manager.js?v=20250626
🔍 解析路径: /js/seo-manager.js
❌ 未匹配到任何语义化URL模式: /js/seo-manager.js
✅ 静态文件请求: /js/seo-manager.js
[127.0.0.1] "GET /js/seo-manager.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/page-router.js?v=20250626
🔍 解析路径: /js/page-router.js
❌ 未匹配到任何语义化URL模式: /js/page-router.js
✅ 静态文件请求: /js/page-router.js
[127.0.0.1] "GET /js/page-router.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/mobile-menu.js?v=20250626
🔍 解析路径: /js/mobile-menu.js
❌ 未匹配到任何语义化URL模式: /js/mobile-menu.js
✅ 静态文件请求: /js/mobile-menu.js
[127.0.0.1] "GET /js/mobile-menu.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/components/breadcrumb.js?v=20250626
🔍 解析路径: /js/components/breadcrumb.js
❌ 未匹配到任何语义化URL模式: /js/components/breadcrumb.js
✅ 静态文件请求: /js/components/breadcrumb.js
[127.0.0.1] "GET /js/components/breadcrumb.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/social-meta.js?v=20250626
🔍 解析路径: /js/social-meta.js
❌ 未匹配到任何语义化URL模式: /js/social-meta.js
✅ 静态文件请求: /js/social-meta.js
[127.0.0.1] "GET /js/social-meta.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /js/global-fix.js?v=20250626
🔍 解析路径: /js/global-fix.js
❌ 未匹配到任何语义化URL模式: /js/global-fix.js
✅ 静态文件请求: /js/global-fix.js
[127.0.0.1] "GET /js/global-fix.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /components/navigation.html
🔍 解析路径: /components/navigation.html
❌ 未匹配到任何语义化URL模式: /components/navigation.html
✅ 静态文件请求: /components/navigation.html
[127.0.0.1] "GET /components/navigation.html HTTP/1.1" 304 -
🔍 收到GET请求: /js/components/navigation.js
🔍 解析路径: /js/components/navigation.js
❌ 未匹配到任何语义化URL模式: /js/components/navigation.js
✅ 静态文件请求: /js/components/navigation.js
[127.0.0.1] "GET /js/components/navigation.js HTTP/1.1" 304 -
🔍 收到GET请求: /components/footer.html
🔍 解析路径: /components/footer.html
❌ 未匹配到任何语义化URL模式: /components/footer.html
✅ 静态文件请求: /components/footer.html
[127.0.0.1] "GET /components/footer.html HTTP/1.1" 304 -
🔍 收到GET请求: /js/components/footer.js
🔍 解析路径: /js/components/footer.js
❌ 未匹配到任何语义化URL模式: /js/components/footer.js
✅ 静态文件请求: /js/components/footer.js
[127.0.0.1] "GET /js/components/footer.js HTTP/1.1" 304 -
🔍 收到GET请求: /quotes/js/pages/quotes.js
🔍 解析路径: /quotes/js/pages/quotes.js
❌ 未匹配到任何语义化URL模式: /quotes/js/pages/quotes.js
🔧 修复静态文件路径: /quotes/js/pages/quotes.js → /js/pages/quotes.js
[127.0.0.1] "GET /quotes/js/pages/quotes.js HTTP/1.1" 200 -
🔍 收到GET请求: /favicon.ico
🔍 解析路径: /favicon.ico
❌ 未匹配到任何语义化URL模式: /favicon.ico
未匹配的路径，可能需要添加新的URL模式: /favicon.ico
[127.0.0.1] code 404, message File not found
[127.0.0.1] "GET /favicon.ico HTTP/1.1" 404 -
🔍 收到GET请求: /favicon.ico
🔍 解析路径: /favicon.ico
❌ 未匹配到任何语义化URL模式: /favicon.ico
未匹配的路径，可能需要添加新的URL模式: /favicon.ico
[127.0.0.1] code 404, message File not found
[127.0.0.1] "GET /favicon.ico HTTP/1.1" 404 -
🔍 收到GET请求: /quote/
🔍 解析路径: /quote/
❌ 未匹配到任何语义化URL模式: /quote/
未匹配的路径，可能需要添加新的URL模式: /quote/
[127.0.0.1] code 404, message File not found
[127.0.0.1] "GET /quote/ HTTP/1.1" 404 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 304 -
🔍 收到GET请求: /test-quotes-page.html
🔍 解析路径: /test-quotes-page.html
❌ 未匹配到任何语义化URL模式: /test-quotes-page.html
[127.0.0.1] "GET /test-quotes-page.html HTTP/1.1" 304 -
处理请求时出错: [Errno 32] Broken pipe
[127.0.0.1] code 500, message Internal server error: [Errno 32] Broken pipe
[127.0.0.1] "GET /test-quotes-page.html HTTP/1.1" 500 -
🔍 收到GET请求: /test-quotes-page.html----------------------------------------
Exception occurred during processing of request from ('127.0.0.1', 61177)
Traceback (most recent call last):
  File "/Users/<USER>/Documents/quotese_0503_0629_V1/frontend/semantic_url_server.py", line 206, in do_GET
    super().do_GET()
    ~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 676, in do_GET
    f = self.send_head()
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 771, in send_head
    self.end_headers()
    ~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Documents/quotese_0503_0629_V1/frontend/semantic_url_server.py", line 216, in end_headers
    super().end_headers()
    ~~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 538, in end_headers
    self.flush_headers()
    ~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 542, in flush_headers
    self.wfile.write(b"".join(self._headers_buffer))
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 845, in write
    self._sock.sendall(b)
    ~~~~~~~~~~~~~~~~~~^^^
BrokenPipeError: [Errno 32] Broken pipe

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 318, in _handle_request_noblock
    self.process_request(request, client_address)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 349, in process_request
    self.finish_request(request, client_address)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/quotese_0503_0629_V1/frontend/semantic_url_server.py", line 22, in __init__
    super().__init__(*args, **kwargs)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 672, in __init__
    super().__init__(*args, **kwargs)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 766, in __init__
    self.handle()
    ~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 436, in handle
    self.handle_one_request()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 424, in handle_one_request
    method()
    ~~~~~~^^
  File "/Users/<USER>/Documents/quotese_0503_0629_V1/frontend/semantic_url_server.py", line 209, in do_GET
    self.send_error(500, f"Internal server error: {e}")
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 488, in send_error
    self.end_headers()
    ~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Documents/quotese_0503_0629_V1/frontend/semantic_url_server.py", line 216, in end_headers
    super().end_headers()
    ~~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 538, in end_headers
    self.flush_headers()
    ~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 542, in flush_headers
    self.wfile.write(b"".join(self._headers_buffer))
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 845, in write
    self._sock.sendall(b)
    ~~~~~~~~~~~~~~~~~~^^^
BrokenPipeError: [Errno 32] Broken pipe
----------------------------------------
----------------------------------------
Exception occurred during processing of request from ('127.0.0.1', 61178)
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 318, in _handle_request_noblock
    self.process_request(request, client_address)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 349, in process_request
    self.finish_request(request, client_address)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/quotese_0503_0629_V1/frontend/semantic_url_server.py", line 22, in __init__
    super().__init__(*args, **kwargs)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 672, in __init__
    super().__init__(*args, **kwargs)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 766, in __init__
    self.handle()
    ~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 436, in handle
    self.handle_one_request()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py", line 404, in handle_one_request
    self.raw_requestline = self.rfile.readline(65537)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
ConnectionResetError: [Errno 54] Connection reset by peer
----------------------------------------

🔍 解析路径: /test-quotes-page.html
❌ 未匹配到任何语义化URL模式: /test-quotes-page.html
[127.0.0.1] "GET /test-quotes-page.html HTTP/1.1" 200 -
处理请求时出错: [Errno 32] Broken pipe
[127.0.0.1] code 500, message Internal server error: [Errno 32] Broken pipe
[127.0.0.1] "GET /test-quotes-page.html HTTP/1.1" 500 -
🔍 收到GET请求: /categories/reality/
🔍 解析路径: /categories/reality/
✅ 匹配到语义化URL模式: ^/categories/([a-zA-Z0-9\-_]+)/$ -> category.html
   匹配组: ('reality',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/category.html
   ✅ 重写路径为: /category.html
未匹配的路径，可能需要添加新的URL模式: /categories/reality/
重定向到 category.html
[127.0.0.1] "GET /categories/reality/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/components/quote-card.js?v=20250626
🔍 解析路径: /js/components/quote-card.js
❌ 未匹配到任何语义化URL模式: /js/components/quote-card.js
✅ 静态文件请求: /js/components/quote-card.js
[127.0.0.1] "GET /js/components/quote-card.js?v=20250626 HTTP/1.1" 304 -
🔍 收到GET请求: /favicon.ico
🔍 解析路径: /favicon.ico
❌ 未匹配到任何语义化URL模式: /favicon.ico
未匹配的路径，可能需要添加新的URL模式: /favicon.ico
[127.0.0.1] code 404, message File not found
[127.0.0.1] "GET /favicon.ico HTTP/1.1" 404 -
🔍 收到GET请求: /categories/belief/
🔍 解析路径: /categories/belief/
✅ 匹配到语义化URL模式: ^/categories/([a-zA-Z0-9\-_]+)/$ -> category.html
   匹配组: ('belief',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/category.html
   ✅ 重写路径为: /category.html
未匹配的路径，可能需要添加新的URL模式: /categories/belief/
重定向到 category.html
[127.0.0.1] "GET /categories/belief/ HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/
🔍 解析路径: /quotes/
✅ 匹配到语义化URL模式: ^/quotes/$ -> quotes.html
   匹配组: ()
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quotes.html
   ✅ 重写路径为: /quotes.html
未匹配的路径，可能需要添加新的URL模式: /quotes/
重定向到 quote.html
[127.0.0.1] "GET /quotes/ HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/js/pages/quotes.js
🔍 解析路径: /quotes/js/pages/quotes.js
❌ 未匹配到任何语义化URL模式: /quotes/js/pages/quotes.js
🔧 修复静态文件路径: /quotes/js/pages/quotes.js → /js/pages/quotes.js
[127.0.0.1] "GET /quotes/js/pages/quotes.js HTTP/1.1" 304 -
🔍 收到GET请求: /quotes/232
🔍 解析路径: /quotes/232
❌ 未匹配到任何语义化URL模式: /quotes/232
未匹配的路径，可能需要添加新的URL模式: /quotes/232
重定向到 quote.html
[127.0.0.1] "GET /quotes/232 HTTP/1.1" 200 -
🔍 收到GET请求: /quotes/232
🔍 解析路径: /quotes/232
❌ 未匹配到任何语义化URL模式: /quotes/232
未匹配的路径，可能需要添加新的URL模式: /quotes/232
[127.0.0.1] code 404, message Quote page not found
[127.0.0.1] "GET /quotes/232 HTTP/1.1" 404 -
🔍 收到GET请求: /js/quote-card-click-fix.js
🔍 解析路径: /js/quote-card-click-fix.js
❌ 未匹配到任何语义化URL模式: /js/quote-card-click-fix.js
✅ 静态文件请求: /js/quote-card-click-fix.js
[127.0.0.1] code 404, message File not found
[127.0.0.1] "GET /js/quote-card-click-fix.js HTTP/1.1" 404 -
🔍 收到GET请求: /js/components/quote-card.js?v=20250623
🔍 解析路径: /js/components/quote-card.js
❌ 未匹配到任何语义化URL模式: /js/components/quote-card.js
✅ 静态文件请求: /js/components/quote-card.js
[127.0.0.1] "GET /js/components/quote-card.js?v=20250623 HTTP/1.1" 304 -
🔍 收到GET请求: /js/quote-card-click-fix.js
🔍 解析路径: /js/quote-card-click-fix.js
❌ 未匹配到任何语义化URL模式: /js/quote-card-click-fix.js
✅ 静态文件请求: /js/quote-card-click-fix.js
[127.0.0.1] code 404, message File not found
[127.0.0.1] "GET /js/quote-card-click-fix.js HTTP/1.1" 404 -
🔍 收到GET请求: /quotes/499248/
🔍 解析路径: /quotes/499248/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('499248',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quote.html
   ❌ HTML文件不存在: quote.html
   当前目录文件: ['test-api-connectivity.html', 'test-api-endpoints.html', 'debug-homepage-api.html', 'debug-api.html', 'test-api-integration.html', 'test-sources-production-api.html', 'index.html', '.DS_Store', 'test-api-endpoints-fixed.html', 'debug-api-calls.html', 'test-sources-debug.html', 'test-breadcrumb-fix.html', 'seo-deployment-check.html', 'test-pages.html', 'css', 'seo-test.html', 'debug-categories-api.html', 'js', 'test-console-error-detection.html', 'test-healology-fix.html', 'test-entity-id-mapping-system.html', 'test-sources-page.html', 'debug-config-loading.html', 'category.html', 'test-categories-simple.html', '404.html', 'test-graphql.html', 'test-collect-entity-ids.html', 'deployment-verification-report.html', 'simple-categories-test.html', 'author.html', 'test-semantic-url-data-loading.html', 'test-semantic-url-validation.html', 'test-categories-method.html', 'test-all-pages.html', 'semantic_url_server.py', 'components', 'debug-homepage.html', 'test.html', 'test-sources-final-verification.html', 'test-long-slug-analysis.html', 'test-api-fix-verification.html', 'test_server.py', 'test-pages-functionality.js', 'test-api-connection.html', 'authors.html', 'test-api-simple.html', 'test-url-routing-fix.html', 'test-mobile-optimization.html', 'test-categories.html', 'test-entityidmapper-debug.html', 'production-api-test.html', 'categories.html.backup', 'test-url-functionality.html', 'debug-source-healology.html', 'sitemap.xml', 'test-homepage.html', 'test-complete-environment.html', 'search.html', 'test-api.html', 'test-semantic-url-routing.html', 'source.html', 'robots.txt', 'test-life-category-debug.html', 'fonts', 'test-quotes-page.html', 'test-homepage-fix.html', 'sources.html', 'categories.html', '.htaccess']
[127.0.0.1] code 404, message HTML file not found: quote.html
[127.0.0.1] "GET /quotes/499248/ HTTP/1.1" 404 -
🔍 收到GET请求: /categories/friendship/
🔍 解析路径: /categories/friendship/
✅ 匹配到语义化URL模式: ^/categories/([a-zA-Z0-9\-_]+)/$ -> category.html
   匹配组: ('friendship',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/category.html
   ✅ 重写路径为: /category.html
未匹配的路径，可能需要添加新的URL模式: /categories/friendship/
重定向到 category.html
[127.0.0.1] "GET /categories/friendship/ HTTP/1.1" 200 -
🔍 收到GET请求: /categories/destiny/
🔍 解析路径: /categories/destiny/
✅ 匹配到语义化URL模式: ^/categories/([a-zA-Z0-9\-_]+)/$ -> category.html
   匹配组: ('destiny',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/category.html
   ✅ 重写路径为: /category.html
未匹配的路径，可能需要添加新的URL模式: /categories/destiny/
重定向到 category.html
[127.0.0.1] "GET /categories/destiny/ HTTP/1.1" 200 -
🔍 收到GET请求: /authors/kamand-kojouri/
🔍 解析路径: /authors/kamand-kojouri/
✅ 匹配到语义化URL模式: ^/authors/([a-zA-Z0-9\-_]+)/$ -> author.html
   匹配组: ('kamand-kojouri',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/author.html
   ✅ 重写路径为: /author.html
未匹配的路径，可能需要添加新的URL模式: /authors/kamand-kojouri/
重定向到 author.html
[127.0.0.1] "GET /authors/kamand-kojouri/ HTTP/1.1" 200 -
🔍 收到GET请求: /js/quote-card-click-fix.js
🔍 解析路径: /js/quote-card-click-fix.js
❌ 未匹配到任何语义化URL模式: /js/quote-card-click-fix.js
✅ 静态文件请求: /js/quote-card-click-fix.js
[127.0.0.1] code 404, message File not found
[127.0.0.1] "GET /js/quote-card-click-fix.js HTTP/1.1" 404 -
🔍 收到GET请求: /quotes/499098/
🔍 解析路径: /quotes/499098/
✅ 匹配到语义化URL模式: ^/quotes/(\d+)/$ -> quote.html
   匹配组: ('499098',)
   检查文件: /Users/<USER>/Documents/quotese_0503_0629_V1/frontend/quote.html