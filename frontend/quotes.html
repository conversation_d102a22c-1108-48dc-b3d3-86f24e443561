<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>10,000+ Famous Quotes & Inspirational Sayings | Quotese.com</title>
    <meta name="description" content="Discover 10,000+ famous quotes from history's greatest minds. Find inspiration, wisdom, and motivation from renowned authors, leaders, and thinkers.">
    <meta name="keywords" content="famous quotes, inspirational quotes, motivational quotes, wisdom quotes, quote collection">
    <link rel="canonical" href="https://quotese.com/quotes/">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Browse All Famous Quotes - Quotese.com">
    <meta property="og:description" content="Explore our complete collection of famous quotes. Find inspiration and wisdom from great minds.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://quotese.com/quotes/">
    <meta property="og:site_name" content="Quotese.com">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Browse All Famous Quotes - Quotese.com">
    <meta name="twitter:description" content="Explore our complete collection of famous quotes. Find inspiration and wisdom from great minds.">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <!-- Stylesheets -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/css/styles.css" rel="stylesheet">
    <link href="/css/performance-optimizations.css" rel="stylesheet">

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "CollectionPage",
        "name": "Famous Quotes Collection",
        "description": "Browse our complete collection of famous quotes from history's greatest minds and voices.",
        "url": "https://quotese.com/quotes/"
    }
    </script>
</head>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100">
    <!-- Navigation -->
    <div id="navigation-container"></div>

    <main class="container mx-auto px-4 py-8">
        <!-- Breadcrumb Navigation -->
        <nav id="breadcrumb-container" class="mb-6" aria-label="Breadcrumb">
            <!-- Breadcrumb will be loaded dynamically -->
        </nav>

        <!-- Page Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold mb-4 text-gray-900 dark:text-gray-100">
                <i class="fas fa-quote-left mr-3 text-purple-500"></i>
                Famous Quotes Collection
            </h1>
            <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                Discover inspiring quotes from history's greatest minds. Find wisdom, motivation, and life-changing insights from renowned authors, leaders, and thinkers.
            </p>
        </div>

        <!-- Loading State -->
        <div id="loading-container" class="text-center py-12" style="display: none;">
            <div class="loading-spinner mx-auto mb-4"></div>
            <p class="text-gray-600 dark:text-gray-400">Loading quotes...</p>
        </div>

        <!-- Error State -->
        <div id="error-container" class="text-center py-12" style="display: none;">
            <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
            <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-gray-100">Failed to load quotes</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">Please try refreshing the page</p>
            <button onclick="location.reload()" class="btn-primary">
                <i class="fas fa-refresh mr-2"></i>Retry
            </button>
        </div>

        <!-- Search and Controls Bar -->
        <div class="mb-8">
            <div class="flex flex-col md:flex-row gap-4 items-center justify-between max-w-4xl mx-auto">
                <!-- Search Container -->
                <div class="flex-1 max-w-md w-full">
                    <div class="relative">
                        <input type="text" id="search-input" placeholder="Search quotes..."
                               class="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>

                <!-- Sort Controls -->
                <div class="flex items-center gap-2">
                    <label for="sort-select" class="text-sm font-medium text-gray-700 dark:text-gray-300">Sort:</label>
                    <select id="sort-select" class="px-3 py-2 border border-gray-300 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-purple-500">
                        <option value="popularity">Most Popular</option>
                        <option value="recent">Recent</option>
                        <option value="alphabetical">A-Z</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Quotes Display Area -->
        <div id="quotes-container" class="quotes-display">
            <!-- Quotes will be loaded dynamically -->
        </div>

        <!-- Pagination -->
        <div id="pagination-container" class="mt-12"></div>
    </main>

    <!-- Footer -->
    <div id="footer-container"></div>

    <!-- Performance Monitor (Critical) -->
    <script src="/js/performance-monitor.js?v=20250627"></script>

    <!-- Core Scripts (Optimized Loading) -->
    <script src="/js/config.js?v=20250626"></script> <!-- Load config first -->
    <script src="/js/api-client.js?v=20250626"></script> <!-- Load API client with GraphQL support -->
    <script src="/js/theme.js?v=20250626" defer></script>
    <script src="/js/url-handler.js?v=20250626"></script>
    <script src="/js/seo-manager.js?v=20250626" defer></script>
    <script src="/js/page-router.js?v=20250626"></script>
    <script src="/js/mobile-menu.js?v=20250626" defer></script>
    <script src="/js/components/pagination.js?v=20250626" defer></script>
    <script src="/js/components/breadcrumb.js?v=20250626" defer></script>
    <script src="/js/navigation-state.js?v=20250627" defer></script>
    <script src="/js/social-meta.js?v=20250626" defer></script>

    <!-- Page Scripts -->
    <script src="/js/pages/quotes.js?v=20250626"></script>

    <!-- Component Loader -->
    <script src="/js/component-loader.js?v=20250626"></script>

    <!-- Optimized Navigation System (for hot module compatibility) -->
    <script src="/js/entity-id-mapper.js?v=20250627"></script>
    <script src="/js/optimized-navigation.js?v=20250626"></script>

    <!-- Initialize Page -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Quotes page DOM loaded, initializing...');

            // Debug: Check all dependencies
            console.log('🔍 Checking dependencies...');
            console.log('- PageRouter:', !!window.PageRouter);
            console.log('- ApiClient:', !!window.ApiClient);
            console.log('- ComponentLoader:', !!window.ComponentLoader);
            console.log('- UrlHandler:', !!window.UrlHandler);
            console.log('- initQuotesListPage:', !!window.initQuotesListPage);

            // Wait a bit for all scripts to load, then initialize
            setTimeout(() => {
                console.log('⏰ Delayed initialization starting...');

                // Try direct initialization first
                if (window.initQuotesListPage) {
                    console.log('🎯 Calling initQuotesListPage directly...');
                    window.initQuotesListPage({}).then(() => {
                        console.log('✅ Direct initialization successful');
                    }).catch(error => {
                        console.error('❌ Direct initialization failed:', error);
                        showErrorFallback();
                    });
                } else {
                    console.error('❌ No initialization method available');
                    showErrorFallback();
                }
            }, 1000); // Wait 1 second for all scripts to load

            function showErrorFallback() {
                console.log('🚨 Showing error fallback...');
                const errorContainer = document.getElementById('error-container');
                const loadingContainer = document.getElementById('loading-container');

                if (errorContainer) {
                    errorContainer.style.display = 'block';
                    errorContainer.innerHTML = `
                        <div class="text-center py-12">
                            <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
                            <h3 class="text-lg font-semibold mb-2">页面加载失败</h3>
                            <p class="text-gray-600 mb-4">请检查浏览器控制台获取详细错误信息</p>
                            <button onclick="location.reload()" class="btn-primary">
                                <i class="fas fa-refresh mr-2"></i>刷新页面
                            </button>
                        </div>
                    `;
                }

                if (loadingContainer) {
                    loadingContainer.style.display = 'none';
                }
            }
        });
    </script>
</body>
</html>
