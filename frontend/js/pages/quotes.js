/**
 * Quotes List Page Controller
 * Independent implementation for /quotes/ page
 * Uses same APIs as other list pages but with quotes-specific logic
 * Architecture pattern consistent with Categories, Authors and Sources pages
 */

// Page state - using independent namespace to avoid conflicts
const quotesListPageState = {
    // Data state
    allQuotes: [],
    displayedQuotes: [],
    filteredQuotes: [],
    
    // Pagination state
    currentPage: 1,
    pageSize: 20,  // Standard quote display: 20 per page
    totalPages: 0,
    totalCount: 0,
    
    // UI state
    isLoading: false,
    searchQuery: '',
    sortOrder: 'popularity',
    viewMode: 'list',
    
    // Performance state
    loadedCount: 0,
    maxQuotes: 1000,
    hasMore: true
};

/**
 * Page initialization function
 * Called by PageRouter: initQuotesListPage()
 */
async function initQuotesListPage(params) {
    try {
        console.log('🚀 Initializing Quotes List Page...');
        console.log('📋 Params received:', params);
        console.log('🔍 Available global objects:', {
            ApiClient: !!window.ApiClient,
            ComponentLoader: !!window.ComponentLoader,
            PageRouter: !!window.PageRouter,
            UrlHandler: !!window.UrlHandler
        });

        // Show loading state
        showLoadingState();
        console.log('⏳ Loading state shown');

        // Load page components
        try {
            await loadPageComponents();
            console.log('✅ Page components loaded');
        } catch (componentError) {
            console.warn('⚠️ Component loading failed, continuing anyway:', componentError);
        }

        // Load quotes data
        try {
            await loadQuotesData();
            console.log('✅ Quotes data loaded');
        } catch (dataError) {
            console.error('❌ Quotes data loading failed:', dataError);
            throw dataError; // This is critical, so we should fail
        }

        // Initialize UI controls
        try {
            initializeControls();
            console.log('✅ UI controls initialized');
        } catch (controlsError) {
            console.warn('⚠️ UI controls initialization failed:', controlsError);
        }

        // Update page metadata
        updatePageMetadata();
        console.log('✅ Page metadata updated');

        // Hide loading state and show content
        hideLoadingState();
        console.log('✅ Quotes List Page initialization complete');

    } catch (error) {
        console.error('❌ Quotes List Page initialization failed:', error);
        showErrorState(error.message);
        throw error;
    }
}

/**
 * Load page components
 */
async function loadPageComponents() {
    if (window.ComponentLoader) {
        await window.ComponentLoader.loadComponents([
            'navigation',
            'breadcrumb',
            'footer'
        ]);
    }
}

/**
 * Load quotes data
 * Uses ApiClient.getTopQuotes() for consistent API pattern
 */
async function loadQuotesData() {
    try {
        console.log('Loading quotes data...');

        // Check if ApiClient is available
        if (!window.ApiClient) {
            throw new Error('ApiClient not available');
        }

        console.log('ApiClient available, calling getTopQuotes()...');

        // Use getTopQuotes API for consistent pattern with other pages
        let quotesResponse;

        try {
            quotesResponse = await window.ApiClient.getTopQuotes(
                quotesListPageState.currentPage,
                quotesListPageState.pageSize,
                false, // not count only
                true   // use cache
            );
            console.log('🎯 API response received:', {
                type: typeof quotesResponse,
                hasQuotes: !!quotesResponse.quotes,
                quotesLength: quotesResponse.quotes ? quotesResponse.quotes.length : 'N/A',
                totalCount: quotesResponse.totalCount,
                totalPages: quotesResponse.totalPages
            });

            // Detailed data structure validation
            if (quotesResponse.quotes && quotesResponse.quotes.length > 0) {
                const sampleQuote = quotesResponse.quotes[0];
                console.log('📊 Sample quote structure:', {
                    id: sampleQuote.id,
                    content: sampleQuote.content ? sampleQuote.content.substring(0, 50) + '...' : 'N/A',
                    author: sampleQuote.author,
                    categories: sampleQuote.categories,
                    allKeys: Object.keys(sampleQuote)
                });
            }
        } catch (apiError) {
            console.warn('❌ getTopQuotes failed, trying getQuotes fallback:', apiError);
            // Fallback to regular getQuotes if getTopQuotes fails
            try {
                quotesResponse = await window.ApiClient.getQuotes(
                    quotesListPageState.currentPage,
                    quotesListPageState.pageSize,
                    {}, // no filters
                    true // use cache
                );
                console.log('🔄 Fallback API response received:', quotesResponse);
            } catch (fallbackError) {
                console.error('❌ Both API calls failed:', fallbackError);
                // Use mock data as last resort
                quotesResponse = getMockQuotes();
                console.log('🎭 Using mock data as fallback');
            }
        }

        // Validate and process data
        if (!quotesResponse || !quotesResponse.quotes || !Array.isArray(quotesResponse.quotes)) {
            throw new Error('Invalid quotes data received from API');
        }

        if (quotesResponse.quotes.length === 0) {
            console.warn('⚠️ No quotes data received, using mock data');
            quotesResponse = getMockQuotes();
        }

        // Store data in page state
        quotesListPageState.allQuotes = quotesResponse.quotes;
        quotesListPageState.filteredQuotes = [...quotesResponse.quotes];
        quotesListPageState.totalCount = quotesResponse.totalCount || quotesResponse.quotes.length;
        quotesListPageState.totalPages = quotesResponse.totalPages || Math.ceil(quotesListPageState.totalCount / quotesListPageState.pageSize);
        quotesListPageState.currentPage = quotesResponse.currentPage || 1;

        console.log('📊 Quotes data processed:', {
            totalQuotes: quotesListPageState.totalCount,
            totalPages: quotesListPageState.totalPages,
            currentPage: quotesListPageState.currentPage,
            pageSize: quotesListPageState.pageSize
        });

        // Apply initial sorting and pagination
        applySorting();
        updatePagination();
        renderQuotes();

    } catch (error) {
        console.error('❌ Failed to load quotes data:', error);
        throw error;
    }
}

/**
 * Apply sorting to quotes
 */
function applySorting() {
    const { sortOrder, filteredQuotes } = quotesListPageState;
    
    switch (sortOrder) {
        case 'popularity':
            // Quotes are already sorted by popularity from API
            break;
        case 'recent':
            filteredQuotes.sort((a, b) => new Date(b.createdAt || b.updatedAt || 0) - new Date(a.createdAt || a.updatedAt || 0));
            break;
        case 'alphabetical':
            filteredQuotes.sort((a, b) => (a.content || '').localeCompare(b.content || ''));
            break;
        default:
            // Keep original order
            break;
    }
    
    console.log(`📊 Applied sorting: ${sortOrder}`);
}

/**
 * Update pagination based on current state
 */
function updatePagination() {
    const { currentPage, totalPages, pageSize, filteredQuotes } = quotesListPageState;
    
    // Calculate displayed quotes for current page
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, filteredQuotes.length);
    quotesListPageState.displayedQuotes = filteredQuotes.slice(startIndex, endIndex);
    
    console.log(`📄 Pagination updated: Page ${currentPage}/${totalPages}, showing ${quotesListPageState.displayedQuotes.length} quotes`);
}

/**
 * Render quotes in the display area
 */
function renderQuotes() {
    const container = document.getElementById('quotes-container');
    if (!container) {
        console.error('❌ Quotes container not found');
        return;
    }

    const { displayedQuotes } = quotesListPageState;
    
    if (displayedQuotes.length === 0) {
        container.innerHTML = `
            <div class="text-center py-12">
                <i class="fas fa-quote-left text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-gray-100">No quotes found</h3>
                <p class="text-gray-600 dark:text-gray-400">Try adjusting your search or filters</p>
            </div>
        `;
        return;
    }

    // Render quotes in a list format
    const quotesHTML = displayedQuotes.map(quote => renderQuoteCard(quote)).join('');
    
    container.innerHTML = `
        <div class="space-y-6">
            ${quotesHTML}
        </div>
    `;
    
    console.log(`✅ Rendered ${displayedQuotes.length} quotes`);
}

/**
 * Render individual quote card
 */
function renderQuoteCard(quote) {
    const authorName = quote.author ? quote.author.name : 'Unknown Author';
    const authorId = quote.author ? quote.author.id : null;
    const categories = quote.categories || [];
    const sources = quote.sources || [];
    
    // Create category links
    const categoryLinks = categories.slice(0, 3).map(cat => 
        `<a href="/categories/${encodeURIComponent(cat.name.toLowerCase())}/" class="text-purple-600 hover:text-purple-800 text-sm">${cat.name}</a>`
    ).join(', ');
    
    // Create source links
    const sourceLinks = sources.slice(0, 2).map(src => 
        `<a href="/sources/${encodeURIComponent(src.name.toLowerCase())}/" class="text-orange-600 hover:text-orange-800 text-sm">${src.name}</a>`
    ).join(', ');
    
    return `
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
            <blockquote class="text-lg text-gray-800 dark:text-gray-200 mb-4 leading-relaxed">
                "${quote.content}"
            </blockquote>
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                <div class="flex items-center">
                    <i class="fas fa-user text-blue-500 mr-2"></i>
                    ${authorId ?
                        `<a href="/authors/${encodeURIComponent(authorName.toLowerCase())}/" class="text-blue-600 hover:text-blue-800 font-medium">${authorName}</a>` :
                        `<span class="text-gray-600 dark:text-gray-400 font-medium">${authorName}</span>`
                    }
                </div>
                <div class="flex flex-col sm:flex-row gap-2 text-sm">
                    ${categoryLinks ? `<div><i class="fas fa-tags text-purple-500 mr-1"></i>${categoryLinks}</div>` : ''}
                    ${sourceLinks ? `<div><i class="fas fa-book text-orange-500 mr-1"></i>${sourceLinks}</div>` : ''}
                </div>
            </div>
        </div>
    `;
}

/**
 * Initialize UI controls
 */
function initializeControls() {
    // Search input
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearch, 300));
        console.log('✅ Search input initialized');
    }

    // Sort select
    const sortSelect = document.getElementById('sort-select');
    if (sortSelect) {
        sortSelect.addEventListener('change', handleSortChange);
        console.log('✅ Sort select initialized');
    }

    // Initialize pagination if component is available
    if (window.PaginationComponent) {
        initializePagination();
        console.log('✅ Pagination initialized');
    }
}

/**
 * Handle search input
 */
function handleSearch(event) {
    const query = event.target.value.trim().toLowerCase();
    quotesListPageState.searchQuery = query;
    quotesListPageState.currentPage = 1; // Reset to first page

    console.log(`🔍 Search query: "${query}"`);

    // Filter quotes based on search query
    if (query === '') {
        quotesListPageState.filteredQuotes = [...quotesListPageState.allQuotes];
    } else {
        quotesListPageState.filteredQuotes = quotesListPageState.allQuotes.filter(quote => {
            const content = (quote.content || '').toLowerCase();
            const authorName = (quote.author && quote.author.name || '').toLowerCase();
            const categories = (quote.categories || []).map(cat => cat.name.toLowerCase()).join(' ');
            const sources = (quote.sources || []).map(src => src.name.toLowerCase()).join(' ');

            return content.includes(query) ||
                   authorName.includes(query) ||
                   categories.includes(query) ||
                   sources.includes(query);
        });
    }

    // Update pagination and render
    quotesListPageState.totalCount = quotesListPageState.filteredQuotes.length;
    quotesListPageState.totalPages = Math.ceil(quotesListPageState.totalCount / quotesListPageState.pageSize);

    applySorting();
    updatePagination();
    renderQuotes();
    updatePaginationComponent();

    console.log(`📊 Search results: ${quotesListPageState.filteredQuotes.length} quotes found`);
}

/**
 * Handle sort change
 */
function handleSortChange(event) {
    quotesListPageState.sortOrder = event.target.value;
    console.log(`📊 Sort order changed to: ${quotesListPageState.sortOrder}`);

    applySorting();
    updatePagination();
    renderQuotes();
}

/**
 * Initialize pagination component
 */
function initializePagination() {
    const paginationContainer = document.getElementById('pagination-container');
    if (!paginationContainer || !window.PaginationComponent) {
        return;
    }

    window.PaginationComponent.init(paginationContainer, {
        currentPage: quotesListPageState.currentPage,
        totalPages: quotesListPageState.totalPages,
        onPageChange: handlePageChange
    });
}

/**
 * Update pagination component
 */
function updatePaginationComponent() {
    if (window.PaginationComponent) {
        window.PaginationComponent.update({
            currentPage: quotesListPageState.currentPage,
            totalPages: quotesListPageState.totalPages
        });
    }
}

/**
 * Handle page change
 */
function handlePageChange(newPage) {
    if (newPage < 1 || newPage > quotesListPageState.totalPages) {
        return;
    }

    quotesListPageState.currentPage = newPage;
    console.log(`📄 Page changed to: ${newPage}`);

    // If we need to load more data from API (for server-side pagination)
    if (quotesListPageState.allQuotes.length < quotesListPageState.totalCount) {
        loadMoreQuotesData(newPage);
    } else {
        // Client-side pagination
        updatePagination();
        renderQuotes();
        updatePaginationComponent();

        // Scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
}

/**
 * Load more quotes data for server-side pagination
 */
async function loadMoreQuotesData(page) {
    try {
        showLoadingState();

        const quotesResponse = await window.ApiClient.getTopQuotes(
            page,
            quotesListPageState.pageSize,
            false,
            true
        );

        if (quotesResponse && quotesResponse.quotes) {
            quotesListPageState.allQuotes = quotesResponse.quotes;
            quotesListPageState.filteredQuotes = [...quotesResponse.quotes];
            quotesListPageState.currentPage = page;

            applySorting();
            updatePagination();
            renderQuotes();
            updatePaginationComponent();
        }

        hideLoadingState();
        window.scrollTo({ top: 0, behavior: 'smooth' });

    } catch (error) {
        console.error('❌ Failed to load more quotes:', error);
        hideLoadingState();
        showErrorState('Failed to load quotes for page ' + page);
    }
}

/**
 * Show loading state
 */
function showLoadingState() {
    const loadingContainer = document.getElementById('loading-container');
    const quotesContainer = document.getElementById('quotes-container');

    if (loadingContainer) {
        loadingContainer.style.display = 'block';
    }
    if (quotesContainer) {
        quotesContainer.style.display = 'none';
    }

    quotesListPageState.isLoading = true;
}

/**
 * Hide loading state
 */
function hideLoadingState() {
    const loadingContainer = document.getElementById('loading-container');
    const quotesContainer = document.getElementById('quotes-container');
    const errorContainer = document.getElementById('error-container');

    if (loadingContainer) {
        loadingContainer.style.display = 'none';
    }
    if (quotesContainer) {
        quotesContainer.style.display = 'block';
    }
    if (errorContainer) {
        errorContainer.style.display = 'none';
    }

    quotesListPageState.isLoading = false;
}

/**
 * Show error state
 */
function showErrorState(message) {
    const errorContainer = document.getElementById('error-container');
    const loadingContainer = document.getElementById('loading-container');
    const quotesContainer = document.getElementById('quotes-container');

    if (errorContainer) {
        errorContainer.style.display = 'block';
        if (message) {
            const errorMessage = errorContainer.querySelector('p');
            if (errorMessage) {
                errorMessage.textContent = message;
            }
        }
    }
    if (loadingContainer) {
        loadingContainer.style.display = 'none';
    }
    if (quotesContainer) {
        quotesContainer.style.display = 'none';
    }
}

/**
 * Update page metadata
 */
function updatePageMetadata() {
    // Update page title with count
    const title = `${quotesListPageState.totalCount.toLocaleString()}+ Famous Quotes & Inspirational Sayings | Quotese.com`;
    document.title = title;

    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
        metaDescription.content = `Discover ${quotesListPageState.totalCount.toLocaleString()}+ famous quotes from history's greatest minds. Find inspiration, wisdom, and motivation from renowned authors, leaders, and thinkers.`;
    }

    // Update Open Graph tags
    const ogTitle = document.querySelector('meta[property="og:title"]');
    if (ogTitle) {
        ogTitle.content = title;
    }

    const ogDescription = document.querySelector('meta[property="og:description"]');
    if (ogDescription) {
        ogDescription.content = `Explore our complete collection of ${quotesListPageState.totalCount.toLocaleString()}+ famous quotes. Find inspiration and wisdom from great minds.`;
    }

    console.log('✅ Page metadata updated');
}

/**
 * Get mock quotes data for fallback
 */
function getMockQuotes() {
    return {
        quotes: [
            {
                id: "1",
                content: "The only way to do great work is to love what you do.",
                author: { id: "1", name: "Steve Jobs" },
                categories: [{ id: "1", name: "Success" }, { id: "2", name: "Work" }],
                sources: [{ id: "1", name: "Stanford Commencement Speech" }],
                createdAt: "2023-01-01T00:00:00Z"
            },
            {
                id: "2",
                content: "Life is what happens to you while you're busy making other plans.",
                author: { id: "2", name: "John Lennon" },
                categories: [{ id: "3", name: "Life" }, { id: "4", name: "Philosophy" }],
                sources: [{ id: "2", name: "Beautiful Boy" }],
                createdAt: "2023-01-02T00:00:00Z"
            },
            {
                id: "3",
                content: "The future belongs to those who believe in the beauty of their dreams.",
                author: { id: "3", name: "Eleanor Roosevelt" },
                categories: [{ id: "5", name: "Dreams" }, { id: "6", name: "Future" }],
                sources: [{ id: "3", name: "Speech" }],
                createdAt: "2023-01-03T00:00:00Z"
            }
        ],
        currentPage: 1,
        pageSize: 20,
        totalPages: 1,
        totalCount: 3
    };
}

/**
 * Debounce function for search input
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Make the initialization function globally available
window.initQuotesListPage = initQuotesListPage;

// Export for module systems if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { initQuotesListPage };
}
