<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Quotes Page - Quotese.com</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">Quotes Page Functionality Test</h1>
        
        <!-- Test Results Container -->
        <div id="test-results" class="space-y-4 mb-8">
            <!-- Test results will be displayed here -->
        </div>
        
        <!-- Manual Test Links -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">Manual Test Links</h2>
            <div class="space-y-2">
                <a href="/quotes/" class="block text-blue-600 hover:text-blue-800">
                    <i class="fas fa-quote-left mr-2"></i>Visit Quotes Page
                </a>
                <a href="/categories/" class="block text-purple-600 hover:text-purple-800">
                    <i class="fas fa-tags mr-2"></i>Visit Categories Page (Reference)
                </a>
                <a href="/authors/" class="block text-blue-600 hover:text-blue-800">
                    <i class="fas fa-users mr-2"></i>Visit Authors Page (Reference)
                </a>
                <a href="/sources/" class="block text-orange-600 hover:text-orange-800">
                    <i class="fas fa-book mr-2"></i>Visit Sources Page (Reference)
                </a>
            </div>
        </div>
        
        <!-- Test Console -->
        <div class="bg-gray-800 text-green-400 rounded-lg p-4 font-mono text-sm">
            <div class="mb-2 text-white">Console Output:</div>
            <div id="console-output" class="max-h-64 overflow-y-auto">
                <!-- Console output will be displayed here -->
            </div>
        </div>
    </div>

    <!-- Core Scripts -->
    <script src="/js/config.js?v=20250626"></script>
    <script src="/js/api-client.js?v=20250626"></script>
    <script src="/js/url-handler.js?v=20250626"></script>
    <script src="/js/page-router.js?v=20250626"></script>
    <script src="/js/pages/quotes.js?v=20250626"></script>
    <script src="/js/optimized-navigation.js?v=20250626"></script>

    <script>
        // Test console output capture
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(type, message) {
            const div = document.createElement('div');
            div.className = type === 'error' ? 'text-red-400' : type === 'warn' ? 'text-yellow-400' : 'text-green-400';
            div.textContent = `[${type.toUpperCase()}] ${message}`;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToConsole('log', args.join(' '));
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToConsole('error', args.join(' '));
        };
        
        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addToConsole('warn', args.join(' '));
        };

        // Test functions
        async function runTests() {
            const testResults = document.getElementById('test-results');
            
            function addTestResult(name, status, details = '') {
                const div = document.createElement('div');
                div.className = `p-4 rounded-lg ${status === 'pass' ? 'bg-green-100 border-green-500' : status === 'fail' ? 'bg-red-100 border-red-500' : 'bg-yellow-100 border-yellow-500'} border-l-4`;
                div.innerHTML = `
                    <div class="flex items-center">
                        <i class="fas ${status === 'pass' ? 'fa-check text-green-600' : status === 'fail' ? 'fa-times text-red-600' : 'fa-clock text-yellow-600'} mr-2"></i>
                        <span class="font-semibold">${name}</span>
                        <span class="ml-2 text-sm ${status === 'pass' ? 'text-green-600' : status === 'fail' ? 'text-red-600' : 'text-yellow-600'}">${status.toUpperCase()}</span>
                    </div>
                    ${details ? `<div class="mt-2 text-sm text-gray-600">${details}</div>` : ''}
                `;
                testResults.appendChild(div);
            }

            console.log('🚀 Starting Quotes Page Tests...');

            // Test 1: Check if required global objects exist
            addTestResult('Global Objects Check', 
                window.ApiClient && window.UrlHandler && window.PageRouter ? 'pass' : 'fail',
                `ApiClient: ${!!window.ApiClient}, UrlHandler: ${!!window.UrlHandler}, PageRouter: ${!!window.PageRouter}`
            );

            // Test 2: Check if quotes page initialization function exists
            addTestResult('Quotes Page Init Function', 
                typeof window.initQuotesListPage === 'function' ? 'pass' : 'fail',
                `initQuotesListPage type: ${typeof window.initQuotesListPage}`
            );

            // Test 3: Check if optimized navigation system is available
            addTestResult('Optimized Navigation System', 
                typeof window.navigateToEntityWithId === 'function' && typeof window.getOptimizedNavigationData === 'function' ? 'pass' : 'fail',
                `navigateToEntityWithId: ${typeof window.navigateToEntityWithId}, getOptimizedNavigationData: ${typeof window.getOptimizedNavigationData}`
            );

            // Test 4: Test API connectivity
            try {
                addTestResult('API Connectivity Test', 'running', 'Testing API connection...');
                const testResponse = await window.ApiClient.getTopQuotes(1, 5, false, true);
                addTestResult('API Connectivity Test', 
                    testResponse && testResponse.quotes ? 'pass' : 'fail',
                    `Response: ${testResponse ? `${testResponse.quotes?.length || 0} quotes, ${testResponse.totalCount || 0} total` : 'No response'}`
                );
            } catch (error) {
                addTestResult('API Connectivity Test', 'fail', `Error: ${error.message}`);
            }

            // Test 5: Test URL handling for quotes page
            const quotesPageType = window.UrlHandler.getCurrentPageType();
            addTestResult('URL Handling Test', 
                quotesPageType === 'quotes-list' || window.location.pathname.includes('/quotes/') ? 'pass' : 'fail',
                `Current page type: ${quotesPageType}, pathname: ${window.location.pathname}`
            );

            // Test 6: Test quotes page initialization
            try {
                addTestResult('Quotes Page Initialization', 'running', 'Testing page initialization...');
                await window.initQuotesListPage({});
                addTestResult('Quotes Page Initialization', 'pass', 'Page initialized successfully');
            } catch (error) {
                addTestResult('Quotes Page Initialization', 'fail', `Error: ${error.message}`);
            }

            console.log('✅ All tests completed');
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 Test page loaded, waiting for scripts...');
            setTimeout(runTests, 2000); // Wait 2 seconds for all scripts to load
        });
    </script>
</body>
</html>
